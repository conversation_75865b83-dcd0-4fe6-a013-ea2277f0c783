# OAuth2 Integration Setup Guide

Detta dokument beskriver hur man konfigurerar OAuth2-integration för eEkonomi och Fortnox i RPA-applikationen.

## Översikt

OAuth2-integrationen möjliggör säker auktorisering med externa tjänster utan att behöva hantera användarnamn och lösenord manuellt. Systemet stöder:

- **eEkonomi (Visma)** - Bokföring och fakturering
- **Fortnox** - Ekonomisystem
- **Automatisk token-förnyelse** - Tokens förnyas automatiskt innan de går ut

## Konfiguration

### Miljövariabler

Lägg till följande miljövariabler i din `.env`-fil:

```env
# Server Configuration
PORT=3002

# eEkonomi OAuth2 Configuration
EEKONOMI_CLIENT_ID=your_eekonomi_client_id
EEKONOMI_CLIENT_SECRET=your_eekonomi_client_secret
EEKONOMI_REDIRECT_URI=http://localhost:3002/api/oauth2/callback/eEkonomi

# Fortnox OAuth2 Configuration
FORTNOX_CLIENT_ID=your_fortnox_client_id
FORTNOX_CLIENT_SECRET=your_fortnox_client_secret
FORTNOX_REDIRECT_URI=http://localhost:3002/api/oauth2/callback/Fortnox
```

### eEkonomi Setup

1. **Registrera din applikation** på [Visma Developer Portal](https://developer.vismaonline.com/)
2. **Konfigurera redirect URI**: `http://localhost:3002/api/oauth2/callback/eEkonomi`
3. **Begär följande scopes**:
   - `ea:api` (required)
   - `offline_access` (required för refresh tokens)
   - `ea:sales` (för försäljning)
   - `ea:accounting` (för bokföring)
   - `ea:purchase` (för inköp)

### Fortnox Setup

1. **Registrera din applikation** på [Fortnox Developer Portal](https://developer.fortnox.se/)
2. **Konfigurera redirect URI**: `http://localhost:3002/api/oauth2/callback/Fortnox`
3. **Begär följande scopes**:
   - `companyinformation` (grundläggande företagsinformation)
   - `article` (artiklar/produkter)
   - `customer` (kunder)
   - `invoice` (fakturor)
   - `voucher` (verifikat)
   - Lägg till fler scopes efter behov enligt [Fortnox Scopes dokumentation](https://www.fortnox.se/developer/guides-and-good-to-know/scopes)
4. **OAuth2 flöde**: Fortnox använder standard OAuth2 Authorization Code flow
5. **Refresh tokens**: Aktiveras automatiskt med `access_type=offline` parameter
6. **Service accounts**: Kan aktiveras med `account_type=service` parameter (kräver systemadministratör)

## Användning

### Skapa OAuth2 Token

1. Gå till **Kunder** > **[Välj kund]** > **API Tokens**
2. Klicka på **"Ny token"**
3. Välj **Provider-typ**: eEkonomi eller Fortnox
4. Klicka på **"Starta OAuth2-integration"**
5. Välj provider och ange tokennamn
6. Klicka på **"Starta OAuth2-flöde"**
7. Auktorisera applikationen i popup-fönstret

### Token-hantering

- **Automatisk förnyelse**: Tokens förnyas automatiskt var 30:e minut
- **Manuell förnyelse**: Klicka på refresh-ikonen i token-listan
- **Status**: Tokens visar status (Aktiv, Utgången, Ej konfigurerad)
- **Utgångsdatum**: Visas för OAuth2-tokens

## API Endpoints

### OAuth2 Providers
```
GET /api/oauth2/providers
```
Hämtar tillgängliga OAuth2-providers och deras konfigurationsstatus.

### Initiera OAuth2-flöde
```
POST /api/oauth2/initiate
{
  "customerId": "customer-id",
  "provider": "eEkonomi",
  "tokenName": "Min eEkonomi Token",
  "description": "Token för bokföring"
}
```

### Förnya token
```
POST /api/oauth2/refresh/:tokenId
```

### Förnya alla tokens
```
POST /api/oauth2/refresh-all
```

## Säkerhet

- **Kryptering**: Alla tokens lagras krypterat i databasen
- **Refresh tokens**: Används för automatisk förnyelse
- **Expiration**: Tokens har utgångsdatum och förnyas automatiskt
- **State parameter**: Används för att förhindra CSRF-attacker

## Felsökning

### Vanliga problem

1. **"OAuth2 provider X is not properly configured"**
   - Kontrollera att miljövariablerna är korrekt inställda
   - Verifiera client ID och client secret

2. **"Invalid redirect URI"**
   - Kontrollera att redirect URI matchar exakt i provider-konfigurationen
   - Använd rätt port (3001 för backend)

3. **"Token refresh failed"**
   - Kontrollera att refresh token fortfarande är giltigt
   - Användaren kan behöva auktorisera om

### Loggar

Kontrollera backend-loggarna för detaljerad information:
```bash
# Backend-loggar
npm run dev
```

Token-refresh-jobbet loggar automatiskt:
- När tokens kontrolleras
- När tokens förnyas
- Eventuella fel

## Utveckling

### Lägga till ny OAuth2 Provider

1. **Uppdatera typer** i `shared/src/types.ts`:
```typescript
export type OAuth2Provider = 'eEkonomi' | 'Fortnox' | 'NewProvider' | 'manual';
```

2. **Lägg till konfiguration** i `backend/src/config/oauth2.ts`:
```typescript
NewProvider: {
  clientId: process.env.NEWPROVIDER_CLIENT_ID || '',
  clientSecret: process.env.NEWPROVIDER_CLIENT_SECRET || '',
  authUrl: 'https://auth.newprovider.com/oauth/authorize',
  tokenUrl: 'https://auth.newprovider.com/oauth/token',
  scopes: ['read', 'write'],
  redirectUri: process.env.NEWPROVIDER_REDIRECT_URI || `http://localhost:${BACKEND_PORT}/api/oauth2/callback/NewProvider`
}
```

3. **Uppdatera UI** i frontend-komponenterna för att visa den nya providern.

## Support

För frågor och support, kontakta utvecklingsteamet eller skapa en issue i projektet.
