import { Router, Request, Response } from 'express';
import Jo<PERSON> from 'joi';
import { OAuth2Provider, InitiateOAuth2Request, OAuth2CallbackRequest, ApiResponse } from '@rpa-project/shared';
import { oauth2Service } from '../services/oauth2Service';
import { validateOAuth2Config } from '../config/oauth2';

const router = Router();

// Validation schemas
const initiateOAuth2Schema = Joi.object({
  customerId: Joi.string().required(),
  provider: Joi.string().valid('eEkonomi', 'Fortnox').required(),
  tokenName: Joi.string().required().min(1).max(100),
  description: Joi.string().optional().max(500)
});

const callbackSchema = Joi.object({
  code: Joi.string().required(),
  state: Joi.string().required()
});

// GET /api/oauth2/providers - Get available OAuth2 providers and their status
router.get('/providers', async (req: Request, res: Response) => {
  try {
    const providers = [
      {
        name: 'eEkonomi',
        displayName: 'eEkonomi (Visma)',
        configured: validateOAuth2Config('eEkonomi'),
        description: 'Visma eEkonomi integration för bokföring och fakturering'
      },
      {
        name: 'Fortnox',
        displayName: 'Fortnox',
        configured: validateOAuth2Config('Fortnox'),
        description: 'Fortnox integration för ekonomisystem'
      }
    ];

    const response: ApiResponse = {
      success: true,
      data: providers
    };
    res.json(response);
  } catch (error) {
    console.error('Error getting OAuth2 providers:', error);
    const response: ApiResponse = {
      success: false,
      error: 'Failed to get OAuth2 providers'
    };
    res.status(500).json(response);
  }
});

// POST /api/oauth2/initiate - Initiate OAuth2 flow
router.post('/initiate', async (req: Request, res: Response) => {
  try {
    // Validate request body
    const { error, value } = initiateOAuth2Schema.validate(req.body);
    if (error) {
      const response: ApiResponse = {
        success: false,
        error: error.details[0].message
      };
      return res.status(400).json(response);
    }

    const request: InitiateOAuth2Request = value;

    // Check if provider is configured
    if (!validateOAuth2Config(request.provider)) {
      const response: ApiResponse = {
        success: false,
        error: `OAuth2 provider ${request.provider} is not properly configured`
      };
      return res.status(400).json(response);
    }

    // Initiate OAuth2 flow
    const authUrl = await oauth2Service.initiateOAuth2Flow(
      request.customerId,
      request.provider,
      request.tokenName,
      request.description
    );

    const response: ApiResponse = {
      success: true,
      data: { authUrl }
    };
    res.json(response);
  } catch (error) {
    console.error('Error initiating OAuth2 flow:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to initiate OAuth2 flow'
    };
    res.status(500).json(response);
  }
});

// GET /api/oauth2/callback/:provider - Handle OAuth2 callback
router.get('/callback/:provider', async (req: Request, res: Response) => {
  try {
    const provider = req.params.provider as OAuth2Provider;
    
    // Validate provider
    if (!['eEkonomi', 'Fortnox'].includes(provider)) {
      return res.status(400).send('Invalid OAuth2 provider');
    }

    // Validate query parameters
    const { error, value } = callbackSchema.validate(req.query);
    if (error) {
      return res.status(400).send(`Invalid callback parameters: ${error.details[0].message}`);
    }

    const { code, state } = value;

    // Handle OAuth2 callback
    await oauth2Service.handleOAuth2Callback(provider, code, state);

    // Redirect to success page or close window
    res.send(`
      <html>
        <head>
          <title>OAuth2 Authorization Complete</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .success { color: #28a745; }
            .container { max-width: 500px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1 class="success">✓ Authorization Complete</h1>
            <p>Your ${provider} account has been successfully connected.</p>
            <p>You can now close this window and return to the application.</p>
            <script>
              // Try to close the window after 3 seconds
              setTimeout(() => {
                window.close();
              }, 3000);
            </script>
          </div>
        </body>
      </html>
    `);
  } catch (error) {
    console.error('OAuth2 callback error:', error);
    res.status(500).send(`
      <html>
        <head>
          <title>OAuth2 Authorization Error</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #dc3545; }
            .container { max-width: 500px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1 class="error">✗ Authorization Failed</h1>
            <p>There was an error connecting your ${req.params.provider} account.</p>
            <p>Please try again or contact support if the problem persists.</p>
            <script>
              setTimeout(() => {
                window.close();
              }, 5000);
            </script>
          </div>
        </body>
      </html>
    `);
  }
});

// POST /api/oauth2/refresh/:tokenId - Refresh OAuth2 token
router.post('/refresh/:tokenId', async (req: Request, res: Response) => {
  try {
    const { tokenId } = req.params;

    await oauth2Service.refreshToken(tokenId);

    const response: ApiResponse = {
      success: true,
      data: { message: 'Token refreshed successfully' }
    };
    res.json(response);
  } catch (error) {
    console.error('Error refreshing OAuth2 token:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to refresh token'
    };
    res.status(500).json(response);
  }
});

// POST /api/oauth2/refresh-all - Manually trigger refresh of all expiring tokens
router.post('/refresh-all', async (req: Request, res: Response) => {
  try {
    await oauth2Service.checkAndRefreshExpiredTokens();

    const response: ApiResponse = {
      success: true,
      data: { message: 'Token refresh check completed successfully' }
    };
    res.json(response);
  } catch (error) {
    console.error('Error running token refresh check:', error);
    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to run token refresh check'
    };
    res.status(500).json(response);
  }
});

export default router;
